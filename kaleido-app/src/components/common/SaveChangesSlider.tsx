import { AnimatePresence, motion } from 'framer-motion';
import { Save, X } from 'lucide-react';
import React from 'react';

interface SaveChangesSliderProps {
  isVisible: boolean;
  onSave: () => void;
  onCancel: () => void;
  isSaving?: boolean;
  saveText?: string;
  cancelText?: string;
  className?: string;
}

export const SaveChangesSlider: React.FC<SaveChangesSliderProps> = ({
  isVisible,
  onSave,
  onCancel,
  isSaving = false,
  saveText = 'Save Changes',
  cancelText = 'Cancel',
  className = '',
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: 120, opacity: 0, scale: 0.95 }}
          animate={{ y: 0, opacity: 1, scale: 1 }}
          exit={{ y: 120, opacity: 0, scale: 0.95 }}
          transition={{
            type: 'spring',
            damping: 20,
            stiffness: 300,
            opacity: { duration: 0.3 },
          }}
          className={`fixed bottom-0 left-0 right-0 z-50 ${className}`}
        >
          {/* Modern centered container */}
          <div className="relative">
            {/* Subtle background glow */}
            <div className="absolute inset-0 bg-gradient-to-t from-purple-600/20 via-purple-500/10 to-transparent blur-xl" />

            {/* Main modern glassmorphic container */}
            <div className="relative bg-gradient-to-r from-slate-900/95 via-purple-950/95 to-slate-900/95 backdrop-blur-xl border-t border-purple-400/30 shadow-2xl">
              {/* Top accent line */}
              <div className="absolute top-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-purple-400/60 to-transparent" />

              {/* Content container with proper centering */}
              <div className="max-w-7xl mx-auto px-6 py-6">
                <div className="flex items-center justify-center">
                  <div className="flex items-center justify-between w-full max-w-4xl">
                    {/* Left side - Modern info section */}
                    <motion.div
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.2, duration: 0.4 }}
                      className="flex items-center space-x-4"
                    >
                      <div className="relative p-2.5 bg-purple-500/20 rounded-xl backdrop-blur-sm border border-purple-400/30">
                        <div className="w-2.5 h-2.5 bg-purple-400 rounded-full animate-pulse" />
                      </div>
                      <div>
                        <p className="text-white font-semibold text-base">
                          You have unsaved changes
                        </p>
                        <p className="text-white/70 text-sm">
                          Save your progress or discard changes
                        </p>
                      </div>
                    </motion.div>

                    {/* Right side - Modern action buttons */}
                    <motion.div
                      initial={{ x: 20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.3, duration: 0.4 }}
                      className="flex items-center space-x-3"
                    >
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={onCancel}
                        className="px-6 py-2.5 bg-white/10 hover:bg-white/15 border border-white/20 hover:border-white/30 text-white/90 rounded-lg transition-all duration-200 flex items-center gap-2 font-medium backdrop-blur-sm"
                      >
                        <X className="w-4 h-4" />
                        {cancelText}
                      </motion.button>

                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={onSave}
                        disabled={isSaving}
                        className="relative px-8 py-2.5 bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white rounded-lg transition-all duration-200 flex items-center gap-2 font-semibold shadow-lg shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden group"
                      >
                        {/* Button shine effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                        <Save
                          className={`w-4 h-4 relative z-10 ${isSaving ? 'animate-spin' : ''}`}
                        />
                        <span className="relative z-10">{saveText}</span>
                      </motion.button>
                    </motion.div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SaveChangesSlider;
