import React from 'react';
import { Save, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface SaveChangesSliderProps {
  isVisible: boolean;
  onSave: () => void;
  onCancel: () => void;
  isSaving?: boolean;
  saveText?: string;
  cancelText?: string;
  className?: string;
}

export const SaveChangesSlider: React.FC<SaveChangesSliderProps> = ({
  isVisible,
  onSave,
  onCancel,
  isSaving = false,
  saveText = 'Save Changes',
  cancelText = 'Cancel',
  className = '',
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className={`fixed bottom-0 left-0 right-0 z-50 ${className}`}
        >
          {/* Background with gradient and glow effect */}
          <div className="relative">
            {/* Purple glow effect */}
            <div className="absolute inset-0 bg-gradient-to-t from-purple-600/20 via-purple-500/10 to-transparent blur-xl" />
            
            {/* Main container */}
            <div className="relative bg-gradient-to-r from-gray-900/95 via-gray-900/98 to-gray-900/95 backdrop-blur-xl border-t border-purple-500/20">
              {/* Inner glow line */}
              <div className="absolute top-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-purple-500/50 to-transparent" />
              
              <div className="container mx-auto px-4 py-6">
                <div className="flex items-center justify-between">
                  {/* Left side - Info text */}
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-500/10 rounded-lg">
                      <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
                    </div>
                    <p className="text-white/80 font-medium">
                      You have unsaved changes
                    </p>
                  </div>
                  
                  {/* Right side - Action buttons */}
                  <div className="flex items-center space-x-3">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={onCancel}
                      className="px-6 py-3 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 text-white/90 rounded-xl transition-all duration-200 flex items-center gap-2 font-medium"
                    >
                      <X className="w-4 h-4" />
                      {cancelText}
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={onSave}
                      disabled={isSaving}
                      className="relative px-8 py-3 bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white rounded-xl transition-all duration-200 flex items-center gap-2 font-semibold shadow-lg shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden group"
                    >
                      {/* Button glow effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-400/0 via-purple-400/30 to-purple-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      
                      <Save className={`w-4 h-4 relative z-10 ${isSaving ? 'animate-spin' : ''}`} />
                      <span className="relative z-10">{saveText}</span>
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SaveChangesSlider;