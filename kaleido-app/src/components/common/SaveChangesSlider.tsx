import { AnimatePresence, motion } from 'framer-motion';
import { Save, X } from 'lucide-react';
import React from 'react';

interface SaveChangesSliderProps {
  isVisible: boolean;
  onSave: () => void;
  onCancel: () => void;
  isSaving?: boolean;
  saveText?: string;
  cancelText?: string;
  className?: string;
}

export const SaveChangesSlider: React.FC<SaveChangesSliderProps> = ({
  isVisible,
  onSave,
  onCancel,
  isSaving = false,
  saveText = 'Save Changes',
  cancelText = 'Cancel',
  className = '',
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: 120, opacity: 0, scale: 0.95 }}
          animate={{ y: 0, opacity: 1, scale: 1 }}
          exit={{ y: 120, opacity: 0, scale: 0.95 }}
          transition={{
            type: 'spring',
            damping: 20,
            stiffness: 300,
            opacity: { duration: 0.3 },
          }}
          className={`fixed bottom-0 left-0 right-0 z-50 ${className}`}
        >
          {/* Enhanced Background with multiple layers */}
          <div className="relative min-h-[120px]">
            {/* Outer glow effect - larger and more prominent */}
            <div className="absolute inset-0 bg-gradient-to-t from-purple-600/30 via-purple-500/15 to-transparent blur-2xl transform scale-110" />

            {/* Secondary glow layer */}
            <div className="absolute inset-0 bg-gradient-to-t from-pink-500/20 via-purple-400/10 to-transparent blur-xl" />

            {/* Animated sliding background elements */}
            <motion.div
              initial={{ x: -100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="absolute top-0 left-0 w-32 h-full bg-gradient-to-r from-purple-500/10 to-transparent"
            />
            <motion.div
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="absolute top-0 right-0 w-32 h-full bg-gradient-to-l from-purple-500/10 to-transparent"
            />

            {/* Main glassmorphic container with increased height */}
            <div className="relative bg-white/10 backdrop-blur-2xl border-t border-white/20 shadow-2xl shadow-purple-500/20">
              {/* Multiple decorative lines */}
              <div className="absolute top-0 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-purple-400/60 to-transparent" />
              <div className="absolute top-[2px] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-white/30 to-transparent" />

              {/* Radial transparency effects */}
              <div className="absolute top-4 left-1/4 w-24 h-24 bg-radial-gradient from-transparent to-white/5 rounded-full blur-sm" />
              <div className="absolute bottom-4 right-1/4 w-32 h-32 bg-radial-gradient from-transparent to-purple-500/5 rounded-full blur-md" />

              {/* Enhanced content container with more padding */}
              <div className="container mx-auto px-6 py-10">
                <div className="flex flex-col space-y-6">
                  {/* Enhanced info section */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.4, duration: 0.5 }}
                    className="flex items-center justify-center space-x-4"
                  >
                    <div className="relative p-3 bg-white/10 rounded-2xl backdrop-blur-sm border border-white/20">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl" />
                      <div className="relative w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse shadow-lg shadow-purple-500/50" />
                    </div>
                    <div className="text-center">
                      <p className="text-white/90 font-semibold text-lg">
                        You have unsaved changes
                      </p>
                      <p className="text-white/60 text-sm mt-1">
                        Save your progress or discard changes
                      </p>
                    </div>
                  </motion.div>

                  {/* Enhanced action buttons */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                    className="flex items-center justify-center space-x-4"
                  >
                    <motion.button
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={onCancel}
                      className="relative px-8 py-4 bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 text-white/90 rounded-2xl transition-all duration-300 flex items-center gap-3 font-medium backdrop-blur-sm shadow-lg hover:shadow-xl group overflow-hidden"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <X className="w-5 h-5 relative z-10" />
                      <span className="relative z-10">{cancelText}</span>
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={onSave}
                      disabled={isSaving}
                      className="relative px-10 py-4 bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white rounded-2xl transition-all duration-300 flex items-center gap-3 font-semibold shadow-2xl shadow-purple-500/40 hover:shadow-purple-500/60 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden group"
                    >
                      {/* Enhanced button effects */}
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-400/0 via-purple-300/40 to-purple-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <div className="absolute inset-0 bg-gradient-to-t from-white/0 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                      <Save className={`w-5 h-5 relative z-10 ${isSaving ? 'animate-spin' : ''}`} />
                      <span className="relative z-10">{saveText}</span>

                      {/* Shimmer effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                        initial={{ x: '-100%' }}
                        animate={{ x: '100%' }}
                        transition={{
                          repeat: Infinity,
                          duration: 2,
                          ease: 'linear',
                          repeatDelay: 3,
                        }}
                      />
                    </motion.button>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SaveChangesSlider;
