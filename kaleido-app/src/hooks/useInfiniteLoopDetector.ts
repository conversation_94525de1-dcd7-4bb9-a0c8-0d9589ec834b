import { useRef, useCallback } from 'react';

interface LoopDetectorOptions {
  maxCalls?: number;
  timeWindow?: number;
  onLoopDetected?: (error: Error) => void;
}

export class InfiniteLoopError extends Error {
  constructor(
    public readonly callCount: number,
    public readonly timeWindow: number,
    public readonly operationName: string
  ) {
    super(
      `Potential infinite loop detected: ${operationName} called ${callCount} times within ${timeWindow}ms`
    );
    this.name = 'InfiniteLoopError';
  }
}

/**
 * Custom hook to detect and prevent infinite loops in async operations
 * 
 * @param operationName - Name of the operation being tracked
 * @param options - Configuration options
 * @returns Function to track calls and detect loops
 */
export function useInfiniteLoopDetector(
  operationName: string,
  options: LoopDetectorOptions = {}
) {
  const {
    maxCalls = 10,
    timeWindow = 1000, // 1 second
    onLoopDetected
  } = options;

  const callTimestamps = useRef<number[]>([]);

  const detectLoop = useCallback(() => {
    const now = Date.now();
    
    // Remove timestamps outside the time window
    callTimestamps.current = callTimestamps.current.filter(
      timestamp => now - timestamp < timeWindow
    );
    
    // Add current timestamp
    callTimestamps.current.push(now);
    
    // Check if we've exceeded max calls
    if (callTimestamps.current.length >= maxCalls) {
      const error = new InfiniteLoopError(
        callTimestamps.current.length,
        timeWindow,
        operationName
      );
      
      // Clear the timestamps to prevent further errors
      callTimestamps.current = [];
      
      // Call custom handler if provided
      if (onLoopDetected) {
        onLoopDetected(error);
      }
      
      // Throw the error to stop execution
      throw error;
    }
  }, [maxCalls, timeWindow, operationName, onLoopDetected]);

  const reset = useCallback(() => {
    callTimestamps.current = [];
  }, []);

  return { detectLoop, reset };
}